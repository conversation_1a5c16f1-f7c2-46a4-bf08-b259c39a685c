import openpyxl
import pandas as pd
import os
import re
import logging
import builtins
_builtin_print = builtins.print
import math # Added for math.ceil

# 导入日志配置
from logger_config import get_bom_processor_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info

# 获取BOM处理日志记录器
logger = get_bom_processor_logger()
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志

# 注释掉自定义 print，恢复标准 print
# def print(*args, **kwargs):
#     if args and isinstance(args[0], str) and args[0].startswith('[BOM匹配]'):
#         _builtin_print(*args, **kwargs)

def str_key_dict(d):
    return {str(k): v for k, v in d.items()} if d else {}

def extract_and_merge_wire_data_by_cabinet(wire_statistics_file):
    """
    从导线统计文件中提取所有导线数据，按屏柜编号分组，对相同编码进行合并（编码格式标准化）
    :param wire_statistics_file: 导线统计文件路径
    :return: {屏柜号: [(导线编码, 合并后长度), ...], ...} 的字典
    """
    cabinet_wire_data = {}
    try:
        # 读取导线统计文件
        with pd.ExcelFile(wire_statistics_file) as xls:
            # 查找"导线统计"和"并线统计"工作表
            target_sheets = []
            for sheet_name in xls.sheet_names:
                if "导线统计" in sheet_name or "并线统计" in sheet_name:
                    target_sheets.append(sheet_name)
            for sheet_name in target_sheets:
                # 读取工作表
                df = pd.read_excel(wire_statistics_file, sheet_name=sheet_name)
                # 查找关键列
                cabinet_col = None
                code_col = None
                length_col = None
                for col in df.columns:
                    col_str = str(col)
                    if '屏柜' in col_str:
                        cabinet_col = col
                    elif '编码' in col_str:
                        code_col = col
                    elif '总长度' in col_str or '长度' in col_str:
                        length_col = col
                if cabinet_col is None or code_col is None or length_col is None:
                    # print(f"警告: {sheet_name} 中缺少必要的列")
                    continue
                # 处理每一行数据
                for idx, row in df.iterrows():
                    cabinet = row[cabinet_col]
                    code = row[code_col]
                    length = row[length_col]
                    # 检查数据有效性
                    if (pd.notna(cabinet) and pd.notna(code) and pd.notna(length) and
                        str(cabinet).strip() not in ['nan', ''] and
                        str(code).strip() not in ['nan', 'N/A', ''] and
                        str(length).strip() not in ['nan', '']):
                        try:
                            cabinet_str = str(cabinet).strip().replace('~', '-')
                            # 标准化编码格式，去除.0后缀
                            code_str = str(code).strip()
                            if code_str.endswith('.0'):
                                code_str = code_str[:-2]
                            length_num = float(length)
                            # 初始化屏柜数据
                            if cabinet_str not in cabinet_wire_data:
                                cabinet_wire_data[cabinet_str] = {}
                            # 合并相同编码的长度
                            if code_str in cabinet_wire_data[cabinet_str]:
                                cabinet_wire_data[cabinet_str][code_str] += length_num
                            else:
                                cabinet_wire_data[cabinet_str][code_str] = length_num
                        except (ValueError, TypeError) as e:
                            # print(f"处理行 {idx} 时出错: {e}")
                            continue
    except Exception as e:
        # print(f"读取导线统计文件时出错: {e}")
        import traceback
        traceback.print_exc()
    # 转换为最终格式
    result = {}
    for cabinet, code_length_dict in cabinet_wire_data.items():
        result[cabinet] = [(code, length) for code, length in code_length_dict.items()]
        # print(f"屏柜 {cabinet} 合并后: {len(result[cabinet])} 种编码")
        # for code, length in result[cabinet]:
        #     print(f"  - 编码: {code}, 总长度: {length}")
    return result

def extract_wire_statistics(wire_statistics_file, cabinet_position):
    """
    从导线统计文件中提取指定屏柜号的导线编码和长度信息（已合并）
    :param wire_statistics_file: 导线统计文件路径
    :param cabinet_position: 屏柜号
    :return: [(导线编码, 长度), ...] 的列表
    """
    # 使用新的合并函数
    all_cabinet_data = extract_and_merge_wire_data_by_cabinet(wire_statistics_file)
    
    # 返回指定屏柜的数据
    if cabinet_position in all_cabinet_data:
        return all_cabinet_data[cabinet_position]
    else:
        # print(f"警告: 未找到屏柜 {cabinet_position} 的导线数据")
        return []

def extract_wire_data_from_dataframes(wire_count_data, parallel_wire_data, printer_data, power_wire_data, multi_core_data, cabinet_position):
    """
    从导线统计、并线统计、数据线记录、电源线记录和多芯线统计DataFrame中提取指定屏柜号的导线编码和长度信息，并进行合并
    :param wire_count_data: 导线统计DataFrame
    :param parallel_wire_data: 并线统计DataFrame
    :param printer_data: 数据线记录DataFrame
    :param power_wire_data: 电源线记录DataFrame
    :param multi_core_data: 多芯线统计DataFrame
    :param cabinet_position: 屏柜号
    :return: [(导线编码, 长度), ...] 的列表
    """
    wire_data = []
    
    def normalize_code(code):
        """标准化编码格式，去除.0后缀"""
        if code is None:
            return None
        code_str = str(code).strip()
        if code_str.endswith('.0'):
            code_str = code_str[:-2]
        return code_str
    
    try:
        # 处理导线统计数据
        if wire_count_data is not None and not wire_count_data.empty:
            # print(f"处理导线统计数据，屏柜: {cabinet_position}")
            cabinet_col = None
            for col in wire_count_data.columns:
                if '屏柜' in str(col):
                    cabinet_col = col
                    break
            
            if cabinet_col is not None:
                cabinet_data = wire_count_data[wire_count_data[cabinet_col] == cabinet_position]
                # print(f"找到导线统计记录: {len(cabinet_data)}条")
                
                for _, row in cabinet_data.iterrows():
                    code = None
                    length = None
                    
                    # 查找编码
                    for col in ['对应编码', '星瀚编码', '编码']:
                        if col in row and row[col] and str(row[col]) != 'nan':
                            code = normalize_code(row[col])
                            break
                    
                    # 查找长度
                    for col in ['导线长度', '总长度', '长度']:
                        if col in row and row[col] and str(row[col]) != 'nan':
                            try:
                                # 提取数值
                                length_str = str(row[col])
                                length_match = re.search(r'(\d+\.?\d*)', length_str)
                                if length_match:
                                    length = float(length_match.group(1))
                                    break
                            except:
                                continue
                    
                    if code and length:
                        wire_data.append((code, length))
                        # print(f"  导线统计: 编码={code}, 长度={length}")
        
        # 处理并线统计数据
        if parallel_wire_data is not None and not parallel_wire_data.empty:
            # print(f"处理并线统计数据，屏柜: {cabinet_position}")
            cabinet_col = None
            for col in parallel_wire_data.columns:
                if '屏柜' in str(col):
                    cabinet_col = col
                    break
            
            if cabinet_col is not None:
                cabinet_data = parallel_wire_data[parallel_wire_data[cabinet_col] == cabinet_position]
                # print(f"找到并线统计记录: {len(cabinet_data)}条")
                
                for _, row in cabinet_data.iterrows():
                    # 处理导线1
                    code1 = None
                    length1 = None
                    
                    # 查找导线1编码
                    for col in ['对应编码1', '星瀚编码1', '编码1']:
                        if col in row and row[col] and str(row[col]) != 'nan':
                            code1 = normalize_code(row[col])
                            break
                    
                    # 查找导线1长度
                    for col in ['导线1长度', '长度1', '单根长度1']:
                        if col in row and row[col] and str(row[col]) != 'nan':
                            try:
                                length_str = str(row[col])
                                length_match = re.search(r'(\d+\.?\d*)', length_str)
                                if length_match:
                                    length1 = float(length_match.group(1))
                                    break
                            except:
                                continue
                    
                    if code1 and length1:
                        wire_data.append((code1, length1))
                        # print(f"  并线统计导线1: 编码={code1}, 长度={length1}")
                    
                    # 处理导线2
                    code2 = None
                    length2 = None
                    
                    # 查找导线2编码
                    for col in ['对应编码2', '星瀚编码2', '编码2']:
                        if col in row and row[col] and str(row[col]) != 'nan':
                            code2 = normalize_code(row[col])
                            break
                    
                    # 查找导线2长度
                    for col in ['导线2长度', '长度2', '单根长度2']:
                        if col in row and row[col] and str(row[col]) != 'nan':
                            try:
                                length_str = str(row[col])
                                length_match = re.search(r'(\d+\.?\d*)', length_str)
                                if length_match:
                                    length2 = float(length_match.group(1))
                                    break
                            except:
                                continue
                    
                    if code2 and length2:
                        wire_data.append((code2, length2))
                        # print(f"  并线统计导线2: 编码={code2}, 长度={length2}")
        
        # 处理数据线记录表
        if printer_data is not None and not printer_data.empty:
            # print(f"处理数据线记录表，屏柜: {cabinet_position}")
            cabinet_col = None
            for col in printer_data.columns:
                if '屏柜' in str(col):
                    cabinet_col = col
                    break
            if cabinet_col is not None:
                cabinet_data = printer_data[printer_data[cabinet_col] == cabinet_position]
                # print(f"找到数据线记录: {len(cabinet_data)}条")
                for _, row in cabinet_data.iterrows():
                    code = None
                    length = None
                    # 查找编码
                    for col in ['对应编码', '编码']:
                        if col in row and row[col] and str(row[col]) != 'nan':
                            code = normalize_code(row[col])
                            break
                    # 查找长度
                    for col in ['固定长度', '长度']:
                        if col in row and row[col] and str(row[col]) != 'nan':
                            try:
                                length_str = str(row[col])
                                length_match = re.search(r'(\d+\.?\d*)', length_str)
                                if length_match:
                                    length = float(length_match.group(1))
                                    break
                            except:
                                continue
                    if code and length:
                        wire_data.append((code, length))
                        # print(f"  数据线记录: 编码={code}, 长度={length}")
        
        # 处理电源线记录表
        if power_wire_data is not None and not power_wire_data.empty:
            # print(f"处理电源线记录表，屏柜: {cabinet_position}")
            cabinet_col = None
            for col in power_wire_data.columns:
                if '屏柜' in str(col):
                    cabinet_col = col
                    break
            if cabinet_col is not None:
                cabinet_data = power_wire_data[power_wire_data[cabinet_col] == cabinet_position]
                # print(f"找到电源线记录: {len(cabinet_data)}条")
                for _, row in cabinet_data.iterrows():
                    code = None
                    count = 1  # 默认根数为1
                    for col in ['对应编码', '编码']:
                        if col in row and row[col] and str(row[col]) != 'nan':
                            code = normalize_code(row[col])
                            break
                    # 获取根数
                    if '根数' in row and row['根数'] and str(row['根数']) != 'nan':
                        try:
                            count = int(row['根数'])
                        except:
                            count = 1
                    if code:
                        # 电源线按根数添加，每根长度为1（或其他默认长度）
                        for i in range(count):
                            wire_data.append((code, 1.0))  # 默认每根长度为1米
                        # print(f"  电源线记录: 编码={code}, 根数={count}, 总长度={count}")

        # 处理多芯线统计数据
        if multi_core_data is not None and not multi_core_data.empty:
            # print(f"处理多芯线统计数据，屏柜: {cabinet_position}")
            cabinet_col = None
            for col in multi_core_data.columns:
                if '屏柜' in str(col):
                    cabinet_col = col
                    break

            if cabinet_col is not None:
                cabinet_data = multi_core_data[multi_core_data[cabinet_col] == cabinet_position]
                # print(f"找到多芯线统计记录: {len(cabinet_data)}条")

                for _, row in cabinet_data.iterrows():
                    code = None
                    length = None

                    # 查找编码列
                    for col in ['对应编码', '编码', '物料编码']:
                        if col in row and pd.notna(row[col]):
                            code = normalize_code(str(row[col]))
                            break

                    # 查找长度列
                    for col in ['单根长度', '长度', '总长度']:
                        if col in row and pd.notna(row[col]):
                            try:
                                length = float(row[col])
                                break
                            except:
                                continue

                    if code and length:
                        wire_data.append((code, length))
                        # print(f"  多芯线统计: 编码={code}, 长度={length}")

    except Exception as e:
        # print(f"处理导线统计数据时出错: {e}")
        import traceback
        traceback.print_exc()

    # 合并相同编码的长度
    merged_data = {}
    for code, length in wire_data:
        if code in merged_data:
            merged_data[code] += length
        else:
            merged_data[code] = length
    
    result = [(code, length) for code, length in merged_data.items()]
    # print(f"合并后结果，屏柜{cabinet_position}: {len(result)}种编码")
    # for code, length in result:
    #     print(f"  - 编码: {code}, 总长度: {length}")
    
    return result

def process_bom_from_design_doc(bom_template_path, material_library_path, small_busbar_data, cabinet_type, output_dir, cabinet_bom_file=None, wire_count_data=None, parallel_wire_data=None, printer_data=None, power_wire_data=None, multi_core_data=None, terminal_counts_df=None, sleeve_counts_df=None, color_band_df=None, material_list_file=None, wire_loss_info=None, decimal_places_func=None, residual_manager=None):
    """
    根据设计说明书的小母线数据和柜式要求，自动填写BOM模板中的分子、分母、星瀚编码。
    支持批量：每个屏柜号生成一个"项目编号-位置.xlsx"BOM清单。
    改进：直接使用DataFrame数据，避免临时文件。
    :param bom_template_path: BOM模板文件路径
    :param material_library_path: 自备料库文件路径
    :param small_busbar_data: 设计说明书解析出的小母线数据
    :param cabinet_type: 柜式要求字符串
    :param output_dir: 输出目录
    :param cabinet_bom_file: 屏柜BOM表（可选）
    :param wire_count_data: 导线统计DataFrame（可选）
    :param parallel_wire_data: 并线统计DataFrame（可选）
    :param printer_data: 数据线记录DataFrame（可选）
    :param power_wire_data: 电源线记录DataFrame（可选）
    :param multi_core_data: 多芯线统计DataFrame（可选）
    :param terminal_counts_df: 压头匹配DataFrame（可选）
    :param sleeve_counts_df: 套管匹配DataFrame（可选）
    :param color_band_df: 色带用量DataFrame（可选）
    :param wire_loss_info: 编码-损耗率字典（可选）
    :param decimal_places_func: 小数位数截断函数（可选）
    :param residual_manager: 残值管理器（可选）
    :return: 生成的BOM文件路径列表
    """
    # 读取自备料库（总表）
    with pd.ExcelFile(material_library_path) as xls:
        all_sheets = xls.sheet_names
        sheet_name = None
        for name in all_sheets:
            if "总表" in name:
                sheet_name = name
                break
        if not sheet_name:
            raise ValueError("自备料库中未找到'总表'工作表")
        total_df = pd.read_excel(material_library_path, sheet_name=sheet_name)

    # 新增：预处理屏柜BOM表，一次性计算所有屏柜的物料总数和顺序
    panel_material_quantities = {}
    panel_material_order = {}  # 新增：记录物料顺序和客供属性
    if cabinet_bom_file:
        try:
            g_column_data = get_g_column_data_from_bom(cabinet_bom_file)
            with pd.ExcelFile(cabinet_bom_file) as bom_xls_pre:
                for sheet_name in bom_xls_pre.sheet_names:
                    df_pre = pd.read_excel(bom_xls_pre, sheet_name=sheet_name, header=0)
                    header_row_idx = None
                    position_col_idx = None
                    code_col_idx = None
                    attr_col_idx = None  # 新增：物料属性列
                    # 查找表头行和关键列
                    for i in range(min(10, df_pre.shape[0])):
                        row_values = [str(cell).strip() for cell in df_pre.iloc[i, :]]
                        if '物料编码' in row_values and '屏柜号' in row_values:
                            header_row_idx = i
                            header = row_values
                            for idx, col_name in enumerate(header):
                                if '屏柜号' in col_name:
                                    position_col_idx = idx
                                if '物料编码' in col_name:
                                    code_col_idx = idx
                                if '物料属性' in col_name:
                                    attr_col_idx = idx
                            break
                    if header_row_idx is not None and position_col_idx is not None and code_col_idx is not None:
                        for row_idx in range(header_row_idx + 1, df_pre.shape[0]):
                            position_val = str(df_pre.iat[row_idx, position_col_idx]).strip().replace('~', '-')
                            code_val = df_pre.iat[row_idx, code_col_idx]
                            attr_val = None
                            if attr_col_idx is not None:
                                attr_val = df_pre.iat[row_idx, attr_col_idx]
                            is_guest = (str(attr_val).strip() == '客供') if attr_val is not None else False
                            if position_val and str(position_val) != 'nan' and code_val and str(code_val) != 'nan':
                                code = str(code_val).strip()
                                if position_val not in panel_material_quantities:
                                    panel_material_quantities[position_val] = {}
                                    panel_material_order[position_val] = {}
                                g_row_data = None
                                for g_item in g_column_data:
                                    if g_item['row'] == row_idx + 1:
                                        g_row_data = g_item
                                        break
                                if g_row_data and g_row_data['value'] is not None:
                                    try:
                                        qty = int(float(g_row_data['value']))
                                    except (ValueError, TypeError):
                                        qty = 0
                                else:
                                    qty = 0
                                # 合并数量，index取第一次出现的
                                if code not in panel_material_quantities[position_val]:
                                    panel_material_quantities[position_val][code] = qty
                                    panel_material_order[position_val][code] = (row_idx, is_guest)
                                else:
                                    panel_material_quantities[position_val][code] += qty
                                    # index不变，is_guest只要有一次是True就为True
                                    old_idx, old_guest = panel_material_order[position_val][code]
                                    panel_material_order[position_val][code] = (old_idx, old_guest or is_guest)
        except Exception as e:
            import traceback
            traceback.print_exc()
            pass

    # 读取屏柜BOM表，获取项目编号和所有屏柜号
    if cabinet_bom_file is None:
        raise ValueError("未提供屏柜BOM表文件")

    # 读取屏柜BOM表
    bom_xls = pd.ExcelFile(cabinet_bom_file)
    
    # 读取所有sheet，查找"合同号"与"屏柜号"
    with pd.ExcelFile(cabinet_bom_file) as bom_xls:
        project_no = None
        project_name = None
        cabinet_positions = []
        for sheet in bom_xls.sheet_names:
            # 先读取前3行，提取合同号、工程名称
            df_head = pd.read_excel(cabinet_bom_file, sheet_name=sheet, header=None, nrows=3)
            try:
                for i in range(df_head.shape[1]):
                    if str(df_head.iat[0, i]).strip() == '合同号':
                        project_no = str(df_head.iat[0, i+1]).strip()
                    if str(df_head.iat[1, i]).strip() == '工程名称':
                        project_name = str(df_head.iat[1, i+1]).strip()
            except Exception:
                pass
            # 用header=2读取正式数据
            df = pd.read_excel(cabinet_bom_file, sheet_name=sheet, header=2)
            # 查找屏柜号列名在任意单元格
            found = False
            for row_idx in range(df.shape[0]):
                for col_idx in range(df.shape[1]):
                    if str(df.columns[col_idx]).strip() == '屏柜号':
                        # 该列下方所有非空值为位置
                        cabinet_col = df.iloc[:, col_idx].dropna().astype(str).tolist()
                        # 将"~"替换为"-"，确保文件名和位置信息的一致性
                        cabinet_positions.extend([c.replace('~', '-') for c in cabinet_col if c and c != 'nan'])
                        found = True
            if found:
                break
        
        if not project_no:
            raise ValueError('屏柜BOM表未找到合同号/项目编号')
        if not cabinet_positions:
            raise ValueError('屏柜BOM表未找到屏柜号')

        unique_positions = []
        seen = set()
        for pos in cabinet_positions:
            if pos not in seen:
                unique_positions.append(pos)
                seen.add(pos)

        # 添加匹配失败记录
        unmatched_materials = []
        
        # 预先获取所有屏柜的配套辅料数据（只调用一次）
        auxiliary_materials = {}
        fixed_ratio_materials = {}
        if material_list_file is not None:
            try:
                auxiliary_materials = get_auxiliary_materials_from_cabinet_bom(cabinet_bom_file, material_list_file)
                fixed_ratio_materials = get_fixed_ratio_auxiliary_materials(material_list_file)
                # print(f"预获取配套辅料数据: {auxiliary_materials}")
                # print(f"预获取固定比例配套辅料数据: {fixed_ratio_materials}")
            except Exception as e:
                # print(f"预获取配套辅料数据时出错: {e}")
                import traceback
                traceback.print_exc()
        
        output_files = []
        residual_list = []  # 新增：记录残值（物料编码、物料名称、残值）

        # 在生成BOM之前，检查并应用残值
        applicable_residuals = {}
        if residual_manager:
            applicable_residuals = residual_manager.check_and_apply_residuals()
            if applicable_residuals:
                # print(f"检测到可应用的残值: {len(applicable_residuals)}项")  # 注释掉ResidualManager输出
                pass

        for position in unique_positions:
            # 检查屏柜号是否包含"FS"，如果包含则跳过
            if "FS" in str(position):
                # print(f"跳过屏柜号 {position}（包含FS标识）")
                continue
            
            wb = openpyxl.load_workbook(bom_template_path)
            ws = wb.active

            # 写入合同号到E2，工程名称到G2，屏柜号到N2
            if ws is not None:
                ws['E2'] = project_no if project_no is not None else ""
                ws['G2'] = project_name if project_name is not None else ""
                ws['N2'] = position

                # 生成配置号：将E2（合同号）和N2（屏柜号）拼接，填入N3列
                contract_no = project_no if project_no is not None else ""
                cabinet_position = str(position) if position is not None else ""
                if contract_no and cabinet_position:
                    config_no = f"{contract_no}-{cabinet_position}"
                    ws['N3'] = config_no
                    log_process_step(logger, f"配置号生成", f"屏柜{position}: {config_no}")
                else:
                    ws['N3'] = ""
                    log_process_step(logger, f"配置号生成", f"屏柜{position}: 空值（缺少合同号或屏柜号）")

            # 处理小母线支架
            bracket_quantity = small_busbar_data.get('bracket', {}).get('quantity')
            bracket_layers = small_busbar_data.get('bracket', {}).get('layers', 1)  # 获取层数信息
            bracket_code = None
            if bracket_quantity and ws is not None:
                # 提取数字部分（每层支架对数）
                quantity_str = str(bracket_quantity)
                match = re.search(r'\d+', quantity_str)
                if match:
                    pairs_per_layer = int(match.group())  # 每层支架对数
                else:
                    raise ValueError(f'小母线支架数量格式不正确: {bracket_quantity}')

                # 支架编码匹配方式：类别为"母线支架"，取"星瀚物料编码"
                bracket_matches = total_df[total_df['类别'] == '母线支架']
                if not bracket_matches.empty:
                    row = bracket_matches.iloc[0]
                    bracket_code = row['星瀚物料编码'] if '星瀚物料编码' in row else row.get('星瀚编码')

                    # 计算总片数：每层对数 × 层数 × 2（每对2片）
                    total_pieces = pairs_per_layer * bracket_layers * 2

                    # 修改分子分母计算规则：
                    # 当片数可被5整除时：分子=片数÷5，分母=1
                    # 当片数不能被5整除时：分子=片数，分母=5
                    ws.cell(row=10, column=4, value=bracket_code)
                    if total_pieces % 5 == 0:
                        ws.cell(row=10, column=8, value=total_pieces // 5)  # 分子：被5除后的数
                        ws.cell(row=10, column=9, value=1)  # 分母：1
                    else:
                        ws.cell(row=10, column=8, value=total_pieces)  # 分子：片数
                        ws.cell(row=10, column=9, value=5)  # 分母：5

                    # 处理支架螺丝
                    # 计算每层螺丝数：≤7对配4个，>7对配8个
                    screws_per_layer = 4 if pairs_per_layer <= 7 else 8
                    total_screws = screws_per_layer * bracket_layers

                    # 支架螺丝固定编码：30301005366
                    screw_code = "30301005366"
                    screw_code_numeric = int(screw_code)
                    ws.cell(row=11, column=4, value=screw_code_numeric)  # 星瀚编码
                    ws.cell(row=11, column=8, value=total_screws)  # 分子
                    ws.cell(row=11, column=9, value=1)  # 分母

                    # 计算分子分母用于日志显示
                    if total_pieces % 5 == 0:
                        numerator = total_pieces // 5
                        denominator = 1
                    else:
                        numerator = total_pieces
                        denominator = 5

                    # print(f"小母线支架处理完成：层数={bracket_layers}, 每层对数={pairs_per_layer}, 总片数={total_pieces}, 分子={numerator}, 分母={denominator}, 总螺丝数={total_screws}")

                else:
                    # 记录匹配失败
                    unmatched_materials.append({
                        '屏柜号': position,
                        '物料类型': '小母线支架',
                        '物料编码': '未找到类别为"母线支架"的物料',
                        '数量': bracket_quantity,
                        '失败原因': '自备料库中未找到类别为"母线支架"的物料'
                    })

            # 处理小母线铜棒
            copper_quantity = small_busbar_data.get('copper', {}).get('quantity')
            copper_spec = small_busbar_data.get('copper', {}).get('spec')
            copper_code = None
            if copper_quantity and copper_spec and ws is not None:
                # 提取数字部分
                copper_quantity_str = str(copper_quantity)
                match = re.search(r'\d+', copper_quantity_str)
                if match:
                    copper_quantity_num = int(match.group())
                else:
                    raise ValueError(f'小母线铜棒数量格式不正确: {copper_quantity}')
                # 铜棒编码匹配方式：类别为"母线铜棒"，物料名称包含规格与柜式组合（如6*56），取"星瀚物料编码"
                # 规格处理
                spec_num = None
                if copper_spec:
                    # 提取数字部分（如φ6mm、6mm、6）
                    match = re.search(r'(\d+\.?\d*)', copper_spec)
                    if match:
                        spec_num = match.group(1)
                length = 56 if ('国网' in str(cabinet_type)) else 49
                if spec_num:
                    material_name_key = f"{spec_num}*{length}"
                    match_row = total_df[(total_df['类别'] == '母线铜棒') & (total_df['物料名称'].astype(str).str.contains(material_name_key))]
                    if not match_row.empty:
                        copper_code = match_row.iloc[0]['星瀚物料编码'] if '星瀚物料编码' in match_row.iloc[0] else match_row.iloc[0].get('星瀚编码')
                        ws.cell(row=12, column=4, value=copper_code)  # 调整为第12行
                    else:
                        # 记录匹配失败
                        unmatched_materials.append({
                            '屏柜号': position,
                            '物料类型': '小母线铜棒',
                            '物料编码': f'规格{spec_num}*{length}',
                            '数量': copper_quantity_num,
                            '失败原因': f'自备料库中未找到类别为"母线铜棒"且物料名称包含"{material_name_key}"的物料'
                        })
                ws.cell(row=12, column=8, value=copper_quantity_num)  # 调整为第12行
                ws.cell(row=12, column=9, value=1)  # 调整为第12行

            # 使用预处理好的物料数据，替换原有的BOM读取和处理逻辑
            position_materials = panel_material_quantities.get(position, {})
            
            if '物料库' in wb.sheetnames:
                material_sheet = wb['物料库']
                if material_sheet is not None:
                    header = [cell.value for cell in next(material_sheet.iter_rows(min_row=1, max_row=1))]
                    xk_idx = header.index('星空编码') if '星空编码' in header else None
                    xh_idx = header.index('星瀚编码') if '星瀚编码' in header else None
                    attr_idx = header.index('物料属性') if '物料属性' in header else None

                    if xk_idx is not None and xh_idx is not None:
                        material_rows = list(material_sheet.iter_rows(min_row=2, values_only=True))
                        
                        # 动态查找BOM清单模板的表头和列索引
                        d_col, h_col, i_col = None, None, None
                        header_row = None
                        bom_sheet = wb.active
                        for i, row in enumerate(bom_sheet.iter_rows(min_row=1, max_row=20)):
                            for cell in row:
                                if cell.value:
                                    if '星瀚编码' in str(cell.value):
                                        d_col = cell.column
                                    if '分子' in str(cell.value):
                                        h_col = cell.column
                                    if '分母' in str(cell.value):
                                        i_col = cell.column
                            if d_col and h_col and i_col:
                                header_row = i + 1
                                break
                        if not (d_col and h_col and i_col and header_row):
                            raise Exception('未能在BOM模板中找到“星瀚编码”、“分子”、“分母”三列')
                        # 确定写入起始行：跳过小母线支架、支架螺丝和铜棒占用的行
                        start_row = header_row + 1
                        has_bracket = bracket_quantity and small_busbar_data.get('bracket', {}).get('quantity')
                        has_copper = copper_quantity and copper_spec and small_busbar_data.get('copper', {}).get('quantity')
                        if has_bracket and has_copper:
                            write_row = 13  # 小母线支架(10行) + 支架螺丝(11行) + 铜棒(12行) = 13行开始
                        elif has_bracket:
                            write_row = 12  # 小母线支架(10行) + 支架螺丝(11行) = 12行开始
                        elif has_copper:
                            write_row = 12  # 铜棒(12行) = 13行开始，但这里没有支架螺丝，所以是12行开始
                        else:
                            write_row = start_row
                        # 跳过已有数据的行
                        while bom_sheet.cell(row=write_row, column=d_col).value:
                            write_row += 1
                        # 使用预处理数据填充BOM物料
                        position_materials = panel_material_quantities.get(position, {})
                        position_order = panel_material_order.get(position, {})
                        # 分离客供物料和其他物料，并保留顺序index
                        guest_materials = []
                        other_materials = []
                        unmatched_with_auxiliary = []  # 新增：未匹配但有固定比例配套辅料的物料
                        for code, qty in position_materials.items():
                            idx, is_guest = position_order.get(code, (0, False))
                            match_rows = [row for row in material_rows if str(row[xk_idx]).strip() == str(code).strip()]
                            if match_rows and attr_idx is not None and str(match_rows[0][attr_idx]).strip() == '客供':
                                is_guest = True
                            xh_code = match_rows[0][xh_idx] if match_rows else None
                            if is_guest:
                                guest_materials.append((code, idx, qty, xh_code))
                            elif match_rows:
                                other_materials.append((code, idx, qty, xh_code))
                            else:
                                # 检查是否有固定比例的配套辅料
                                current_code_num = int(''.join(filter(str.isdigit, str(code))))
                                if current_code_num in fixed_ratio_materials:
                                    # 有固定比例配套辅料，加入特殊处理列表
                                    unmatched_with_auxiliary.append((code, idx, qty, None))
                                else:
                                    # 完全无法匹配的物料
                                    unmatched_materials.append({
                                        '屏柜号': position,
                                        '物料类型': '屏柜BOM物料',
                                        '物料编码': code,
                                        '数量': qty,
                                        '失败原因': f'物料库中未找到星空编码匹配"{code}"的物料'
                                    })
                        # 按index排序
                        guest_materials.sort(key=lambda x: x[1])
                        other_materials.sort(key=lambda x: x[1])
                        unmatched_with_auxiliary.sort(key=lambda x: x[1])
                        # 先写入客供物料
                        for code, idx, qty, xh_code in guest_materials:
                            bom_sheet.cell(row=write_row, column=d_col, value=xh_code)
                            bom_sheet.cell(row=write_row, column=h_col, value=qty)
                            bom_sheet.cell(row=write_row, column=i_col, value=1)
                            write_row += 1
                        # 写入固定编码
                        bom_sheet.cell(row=write_row, column=d_col, value=31002000012)
                        bom_sheet.cell(row=write_row, column=h_col, value=1)
                        bom_sheet.cell(row=write_row, column=i_col, value=1)
                        write_row += 1
                        # 新增：如柜式要求不含“户外”，插入一行空值
                        if "户外" not in str(cabinet_type):
                            bom_sheet.cell(row=write_row, column=d_col, value=None)
                            bom_sheet.cell(row=write_row, column=h_col, value=None)
                            bom_sheet.cell(row=write_row, column=i_col, value=None)
                            write_row += 1
                        # 根据柜式要求写入
                        if "前接线" in str(cabinet_type):
                            bom_sheet.cell(row=write_row, column=d_col, value=60104002373)
                            bom_sheet.cell(row=write_row, column=h_col, value=1)
                            bom_sheet.cell(row=write_row, column=i_col, value=1)
                            write_row += 1
                        elif "上海" in str(cabinet_type):
                            bom_sheet.cell(row=write_row, column=d_col, value=60104002373)
                            bom_sheet.cell(row=write_row, column=h_col, value=1)
                            bom_sheet.cell(row=write_row, column=i_col, value=1)
                            write_row += 1
                        elif "梅勒" in str(cabinet_type):
                            bom_sheet.cell(row=write_row, column=d_col, value=60104002373)
                            bom_sheet.cell(row=write_row, column=h_col, value=1)
                            bom_sheet.cell(row=write_row, column=i_col, value=1)
                            write_row += 1
                        # 再写入其他物料
                        for code, idx, qty, xh_code in other_materials:
                            bom_sheet.cell(row=write_row, column=d_col, value=xh_code)
                            bom_sheet.cell(row=write_row, column=h_col, value=qty)
                            bom_sheet.cell(row=write_row, column=i_col, value=1)
                            write_row += 1

                            # === 新增：把手匹配功能 ===
                            handle_matched = False
                            if material_list_file and str(code) not in ['80101001900', '80101002000']:
                                try:
                                    # 读取把手表
                                    wb_handle = openpyxl.load_workbook(material_list_file)
                                    if '把手' in wb_handle.sheetnames:
                                        sheet_handle = wb_handle['把手']

                                        # 查找"深瑞星空编码"列
                                        handle_code_col_idx = None
                                        for col_idx in range(1, sheet_handle.max_column + 1):
                                            cell_value = sheet_handle.cell(row=1, column=col_idx).value
                                            if cell_value and '深瑞星空编码' in str(cell_value):
                                                handle_code_col_idx = col_idx
                                                break

                                        if handle_code_col_idx:
                                            # 在把手表中查找匹配的编码
                                            for row_idx in range(2, sheet_handle.max_row + 1):
                                                handle_code = sheet_handle.cell(row=row_idx, column=handle_code_col_idx).value
                                                if handle_code and str(handle_code).strip() == str(code).strip():
                                                    # 匹配成功，在下一行留空
                                                    bom_sheet.cell(row=write_row, column=d_col, value=None)
                                                    bom_sheet.cell(row=write_row, column=h_col, value=None)
                                                    bom_sheet.cell(row=write_row, column=i_col, value=None)
                                                    write_row += 1
                                                    handle_matched = True
                                                    break
                                    wb_handle.close()
                                except Exception as e:
                                    print(f"处理把手匹配时出错: {e}")
                                    if 'wb_handle' in locals():
                                        wb_handle.close()
                            
                            # === 新增：处理配套辅料（固定比例和非固定比例） ===
                            current_code_num = int(''.join(filter(str.isdigit, str(code))))

                            # 首先检查是否有固定比例的配套辅料
                            if current_code_num in fixed_ratio_materials:
                                try:
                                    # 获取固定比例配套辅料信息
                                    fixed_ratio_info = fixed_ratio_materials[current_code_num]
                                    main_ratio = fixed_ratio_info['主料比例']
                                    auxiliary_list = fixed_ratio_info['配套辅料']

                                    # 计算配套辅料数量
                                    calculated_auxiliaries = calculate_fixed_ratio_quantities(qty, main_ratio, auxiliary_list)

                                    # 填入计算出的配套辅料
                                    for aux_item in calculated_auxiliaries:
                                        aux_code = aux_item['星瀚编码']
                                        aux_qty = aux_item['分子']

                                        # 确保编码为数字类型
                                        try:
                                            aux_code_to_write = int(aux_code) if str(aux_code).isdigit() else aux_code
                                        except (ValueError, TypeError):
                                            aux_code_to_write = aux_code

                                        bom_sheet.cell(row=write_row, column=d_col, value=aux_code_to_write)
                                        bom_sheet.cell(row=write_row, column=h_col, value=aux_qty)
                                        bom_sheet.cell(row=write_row, column=i_col, value=1)
                                        write_row += 1

                                        # print(f"固定比例配套辅料: 主料{code}(数量{qty}) -> 配套辅料{aux_code}(数量{aux_qty})")

                                except Exception as e:
                                    print(f"处理物料 {code} 的固定比例配套辅料时出错: {e}")
                                    import traceback
                                    traceback.print_exc()

                            # 处理非固定比例的配套辅料（预留空值）
                            elif auxiliary_materials and position in auxiliary_materials:
                                auxiliary_count = 0

                                # 从配套辅料映射中查找当前物料的配套行数
                                try:
                                    # 这里需要重新获取配套辅料映射
                                    wb_aux = openpyxl.load_workbook(material_list_file)
                                    sheet_aux = wb_aux['配套物料']

                                    # 动态获取列索引
                                    remark_col_idx_aux, code_col_idx_aux, _, _ = get_column_indices_from_auxiliary_sheet(sheet_aux)

                                    if remark_col_idx_aux is None or code_col_idx_aux is None:
                                        wb_aux.close()
                                        continue

                                    f_col_merged_aux = []
                                    for merged_range in sheet_aux.merged_cells.ranges:
                                        min_col, min_row, max_col, max_row = merged_range.bounds
                                        if min_col == remark_col_idx_aux and max_col == remark_col_idx_aux:
                                            f_col_merged_aux.append((min_row, max_row))

                                    for min_row, max_row in f_col_merged_aux:
                                        # 跳过固定比例的配套辅料
                                        remark_value = sheet_aux.cell(row=min_row, column=remark_col_idx_aux).value
                                        if remark_value and str(remark_value).strip() == "固定比例":
                                            continue

                                        for row in range(min_row, max_row + 1):
                                            code_value_aux = sheet_aux.cell(row=row, column=code_col_idx_aux).value
                                            if code_value_aux and str(code_value_aux).strip():
                                                code_aux = str(code_value_aux).strip()
                                                try:
                                                    code_num_aux = int(''.join(filter(str.isdigit, code_aux)))
                                                    if code_num_aux == current_code_num:
                                                        auxiliary_count = max_row - min_row + 1
                                                        break
                                                except:
                                                    continue
                                        if auxiliary_count > 0:
                                            break
                                    wb_aux.close()

                                    # 填入配套辅料空值
                                    if auxiliary_count > 0:
                                        # print(f"为物料 {code} 填入 {auxiliary_count} 行配套辅料空值")
                                        for i in range(auxiliary_count):
                                            bom_sheet.cell(row=write_row, column=d_col, value=None)
                                            bom_sheet.cell(row=write_row, column=h_col, value=None)
                                            bom_sheet.cell(row=write_row, column=i_col, value=None)
                                            write_row += 1
                                        # print(f"✅ 已为物料 {code} 填入 {auxiliary_count} 行配套辅料空值")

                                except Exception as e:
                                    print(f"处理物料 {code} 的配套辅料时出错: {e}")
                                    import traceback
                                    traceback.print_exc()
                            # === 配套辅料处理结束 ===

                        # 新增：处理未匹配但有固定比例配套辅料的物料
                        for code, idx, qty, xh_code in unmatched_with_auxiliary:
                            # 主料没有星瀚编码，不填入主料行，但处理其固定比例配套辅料
                            current_code_num = int(''.join(filter(str.isdigit, str(code))))

                            if current_code_num in fixed_ratio_materials:
                                try:
                                    # 获取固定比例配套辅料信息
                                    fixed_ratio_info = fixed_ratio_materials[current_code_num]
                                    main_ratio = fixed_ratio_info['主料比例']
                                    auxiliary_list = fixed_ratio_info['配套辅料']

                                    # 计算配套辅料数量
                                    calculated_auxiliaries = calculate_fixed_ratio_quantities(qty, main_ratio, auxiliary_list)

                                    # 只填入有星瀚编码的配套辅料
                                    for aux_item in calculated_auxiliaries:
                                        aux_code = aux_item['星瀚编码']
                                        aux_qty = aux_item['分子']

                                        # 检查配套辅料是否有有效的星瀚编码
                                        if aux_code and str(aux_code).strip() and str(aux_code).strip() != 'nan':
                                            # 确保编码为数字类型
                                            try:
                                                aux_code_to_write = int(aux_code) if str(aux_code).isdigit() else aux_code
                                            except (ValueError, TypeError):
                                                aux_code_to_write = aux_code

                                            bom_sheet.cell(row=write_row, column=d_col, value=aux_code_to_write)
                                            bom_sheet.cell(row=write_row, column=h_col, value=aux_qty)
                                            bom_sheet.cell(row=write_row, column=i_col, value=1)
                                            write_row += 1

                                            # print(f"未匹配主料的固定比例配套辅料: 主料{code}(数量{qty}) -> 配套辅料{aux_code}(数量{aux_qty})")

                                except Exception as e:
                                    print(f"处理未匹配主料 {code} 的固定比例配套辅料时出错: {e}")
                                    import traceback
                                    traceback.print_exc()

                        # 新增：在导线数据之前填入6行空值
                        # print(f"在导线数据之前为屏柜 {position} 填入6行空值")
                        for i in range(6):
                            bom_sheet.cell(row=write_row, column=d_col, value=None)  # 星瀚编码列填入空值
                            bom_sheet.cell(row=write_row, column=h_col, value=None)  # 数量列填入空值
                            bom_sheet.cell(row=write_row, column=i_col, value=None)  # 单位列填入空值
                            write_row += 1
                        # print(f"✅ 已为屏柜 {position} 填入6行空值，当前写入行号: {write_row}")
                        
                        # 使用DataFrame数据提取导线信息
                        if wire_count_data is not None or parallel_wire_data is not None or printer_data is not None or power_wire_data is not None or multi_core_data is not None:
                            wire_data = extract_wire_data_from_dataframes(wire_count_data, parallel_wire_data, printer_data, power_wire_data, multi_core_data, position)
                        else:
                            print(f"警告：未提供导线统计数据，跳过导线数据处理")
                            pass
                        # 填入导线数据
                        wire_loss_info = str_key_dict(wire_loss_info)
                        for wire_code, wire_length in wire_data:
                            # wire_code本身就是str类型，直接用
                            loss_rate = 0.0
                            min_valid_decimals = wire_loss_info[wire_code].get('最小有效值小数位数', 2)
                            if min_valid_decimals is None:
                                min_valid_decimals = 2
                            material_name = ''
                            if wire_loss_info and wire_code in wire_loss_info:
                                material_name = wire_loss_info[wire_code].get('物料名称', '')
                                # print(f"物料名称: {material_name}")
                                lr = wire_loss_info[wire_code].get('损耗率')
                                if lr is not None:
                                    try:
                                        loss_rate = float(lr)
                                    except (ValueError, TypeError):
                                        loss_rate = 0.0
                            # 使用decimal模块进行精确计算
                            from decimal_calculator import calculate_wire_residual
                            try:
                                wire_length_num = float(wire_length)
                            except (ValueError, TypeError):
                                wire_length_num = 0.0

                            # 使用decimal模块计算精确的截断值和残值
                            truncated_molecule, residual = calculate_wire_residual(
                                wire_length_num, loss_rate, min_valid_decimals
                            )

                            # 检查是否有可应用的残值
                            if wire_code in applicable_residuals:
                                apply_amount = applicable_residuals[wire_code]['应用数量']
                                truncated_molecule += apply_amount
                                # print(f"应用残值: 物料{wire_code} 增加{apply_amount}, 新分子值: {truncated_molecule}")  # 注释掉ResidualManager输出

                            # 记录残值（使用更精确的阈值判断）
                            if abs(residual) > 1e-15:  # 使用更严格的阈值
                                residual_list.append({'物料编码': wire_code, '物料名称': material_name, '残值': residual})
                            # 写入BOM，保证编码为数字类型
                            try:
                                wire_code_to_write = int(wire_code)
                            except (ValueError, TypeError):
                                wire_code_to_write = wire_code
                            bom_sheet.cell(row=write_row, column=d_col, value=wire_code_to_write)
                            bom_sheet.cell(row=write_row, column=h_col, value=truncated_molecule)
                            bom_sheet.cell(row=write_row, column=i_col, value=1)
                            write_row += 1
                        # 填入压头匹配数据
                        if terminal_counts_df is not None and not terminal_counts_df.empty:
                            cabinet_terminals = terminal_counts_df[terminal_counts_df['屏柜编号'] == position]
                            if not cabinet_terminals.empty:
                                # print(f"为屏柜 {position} 填入 {len(cabinet_terminals)} 种压头")
                                for _, terminal_row in cabinet_terminals.iterrows():
                                    try:
                                        terminal_code = terminal_row['压头星瀚编码']
                                        terminal_qty = terminal_row['数量']
                                        if str(terminal_code).isdigit():
                                            terminal_code_num = int(terminal_code)
                                        else:
                                            terminal_code_num = terminal_code
                                        terminal_qty_num = float(terminal_qty)
                                        bom_sheet.cell(row=write_row, column=d_col, value=terminal_code_num)
                                        bom_sheet.cell(row=write_row, column=h_col, value=terminal_qty_num)
                                        bom_sheet.cell(row=write_row, column=i_col, value=1)
                                        write_row += 1
                                        # print(f"  压头: {terminal_code_num}, 数量: {terminal_qty_num}")
                                    except (ValueError, TypeError) as e:
                                        # print(f"压头数据转换失败：编码={terminal_row.get('压头星瀚编码')}, 数量={terminal_row.get('数量')}, 错误: {e}")
                                        continue
                            else:
                                # print(f"屏柜 {position} 无压头匹配数据")
                                pass
                        else:
                            # print(f"未提供压头匹配数据，跳过压头填入")
                            pass

                        # === 新套管数据处理逻辑 ===
                        if sleeve_counts_df is not None and not sleeve_counts_df.empty:
                            # 先按屏柜编号+套管星瀚编码分组，合并套管总长度，取首个系统单位长度、损耗率、最小有效值、最小有效值小数位数
                            grouped = sleeve_counts_df.groupby([
                                '屏柜编号', '套管星瀚编码'
                            ], as_index=False).agg({
                                '套管总长度': 'sum',
                                '系统单位长度': 'first',
                                '损耗率': 'first',
                                '最小有效值': 'first',
                                '最小有效值小数位数': 'first'
                            })
                            cabinet_sleeves = grouped[grouped['屏柜编号'] == position]
                            if not cabinet_sleeves.empty:
                                for _, row in cabinet_sleeves.iterrows():
                                    try:
                                        sleeve_code = row['套管星瀚编码']
                                        total_length = row['套管总长度']
                                        unit_length = row['系统单位长度']
                                        loss_rate = row['损耗率'] if '损耗率' in row and pd.notna(row['损耗率']) else 0.0
                                        min_valid_decimals = int(row['最小有效值小数位数']) if '最小有效值小数位数' in row and pd.notna(row['最小有效值小数位数']) else 2
                                        # 使用decimal模块进行精确的套管计算（但不记录残值，避免重复）
                                        from decimal_calculator import calculate_sleeve_residual
                                        truncated_value, sleeve_residual = calculate_sleeve_residual(
                                            total_length, unit_length, loss_rate, min_valid_decimals
                                        )
                                        molecule = truncated_value

                                        # 注意：套管残值已经在sleeve_matching.py中记录，这里不再重复记录到residual_list
                                        if str(sleeve_code).isdigit():
                                            sleeve_code_num = int(sleeve_code)
                                        else:
                                            sleeve_code_num = sleeve_code
                                        bom_sheet.cell(row=write_row, column=d_col, value=sleeve_code_num) # 星瀚编码列
                                        bom_sheet.cell(row=write_row, column=h_col, value=molecule) # 分子列
                                        bom_sheet.cell(row=write_row, column=i_col, value=1) # 单位列
                                        write_row += 1
                                    except (ValueError, TypeError) as e:
                                        continue
                        # === 新套管数据处理逻辑结束 ===

                        # 新增：填入色带用量数据
                        if color_band_df is not None and not color_band_df.empty:
                            cabinet_bands = color_band_df[color_band_df['屏柜编号'] == position]
                            if not cabinet_bands.empty:
                                # print(f"为屏柜 {position} 填入 {len(cabinet_bands)} 种色带")
                                for _, row in cabinet_bands.iterrows():
                                    try:
                                        band_code = row['色带星瀚编码']
                                        band_qty = row['色带系统分子']
                                        # 编码和数量格式
                                        if str(band_code).isdigit():
                                            band_code_num = int(band_code)
                                        else:
                                            band_code_num = band_code
                                        band_qty_num = float(band_qty)
                                        bom_sheet.cell(row=write_row, column=d_col, value=band_code_num) # 星瀚编码列
                                        bom_sheet.cell(row=write_row, column=h_col, value=band_qty_num) # 数量列
                                        bom_sheet.cell(row=write_row, column=i_col, value=1) # 单位列
                                        write_row += 1
                                        # print(f"  色带: {band_code_num}, 系统分子: {band_qty_num}")
                                    except (ValueError, TypeError) as e:
                                        # print(f"色带数据转换失败：编码={row.get('色带星瀚编码')}, 系统分子={row.get('色带系统分子')}, 错误: {e}")
                                        continue
                            else:
                                # print(f"屏柜 {position} 无色带用量数据")
                                pass
                        else:
                            # print(f"未提供色带用量数据，跳过色带填入")
                            pass

                        # 新增：填入配套辅料数据
                        # if auxiliary_materials:
                        #     try:
                        #         # 使用预先获取的配套辅料数据
                        #         write_row = write_auxiliary_materials_to_bom(
                        #             auxiliary_materials, 
                        #             material_library_path, 
                        #             bom_sheet, 
                        #             write_row, 
                        #             d_col,  # 星瀚编码列
                        #             h_col,  # 数量列
                        #             i_col,  # 单位列
                        #             position
                        #         )
                        #     except Exception as e:
                        #         # print(f"处理配套辅料时出错: {e}")
                        #         import traceback
                        #         traceback.print_exc()
                        # else:
                        #     # print(f"无配套辅料数据，跳过配套辅料处理")
                        #     pass

                        # 输出文件名
                        output_path = os.path.join(output_dir, f'{project_no}-{position}.xlsx')
                        wb.save(output_path)
                        output_files.append(output_path)
        
        # 生成匹配失败物料报告
        if unmatched_materials:
            unmatched_df = pd.DataFrame(unmatched_materials)
            # 按屏柜号分组
            unmatched_report_path = os.path.join(output_dir, f'{project_no}-匹配失败物料报告.xlsx')
            try:
                with pd.ExcelWriter(unmatched_report_path, engine='openpyxl') as writer:
                    # 总览表
                    unmatched_df.to_excel(writer, sheet_name='匹配失败总览', index=False)
                    # 按屏柜号分别生成表格
                    unique_cabinets = unmatched_df['屏柜号'].unique()
                    for cabinet_pos in unique_cabinets:
                        cabinet_data = unmatched_df[unmatched_df['屏柜号'] == cabinet_pos]
                        sheet_name = f'屏柜{cabinet_pos}' if len(str(cabinet_pos)) <= 25 else f'屏柜{str(cabinet_pos)[:25]}'
                        cabinet_data.to_excel(writer, sheet_name=sheet_name, index=False)
            
                # print(f"匹配失败物料报告已生成: {unmatched_report_path}")
                # print(f"共发现 {len(unmatched_materials)} 个匹配失败的物料")
                output_files.append(unmatched_report_path)
            except Exception as e:
                # print(f"生成匹配失败物料报告时出错: {e}")
                # 备用方案：直接保存到CSV
                csv_path = os.path.join(output_dir, f'{project_no}-匹配失败物料报告.csv')
                unmatched_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                # print(f"已生成CSV格式报告: {csv_path}")
                output_files.append(csv_path)
        
        # ========== 新增后处理：统计后补录分子数量 ==========
        from openpyxl import load_workbook
        for position, code_qty_dict in panel_material_quantities.items():
            # 1. 先在BOM清单C列匹配
            bom_path = os.path.join(output_dir, f'{project_no}-{position}.xlsx')
            if os.path.exists(bom_path):
                wb = load_workbook(bom_path)
                ws = wb.active
                # 查找表头行和C、H列索引
                header_row = None
                c_col, h_col = None, None
                for i, row in enumerate(ws.iter_rows(min_row=1, max_row=10)):
                    for cell in row:
                        if cell.value == '星空编码':
                            c_col = cell.column
                        if cell.value == '分子':
                            h_col = cell.column
                    if c_col and h_col:
                        header_row = i + 1
                        break
                if c_col and h_col and header_row:
                    for code, qty in code_qty_dict.items():
                        matched = False
                        for r in range(header_row+1, ws.max_row+1):
                            cell_code = ws.cell(row=r, column=c_col).value
                            if str(cell_code).strip() == str(code).strip():
                                ws.cell(row=r, column=h_col, value=qty)
                                matched = True
                        if not matched:
                            # 2. 匹配失败则去匹配失败物料报告补录
                            fail_path = os.path.join(output_dir, f'{project_no}-匹配失败物料报告.xlsx')
                            if os.path.exists(fail_path):
                                fail_wb = load_workbook(fail_path)
                                for sheet in fail_wb.sheetnames:
                                    ws_fail = fail_wb[sheet]
                                    # 查找表头行和C、D列
                                    fail_header_row, fail_c_col, fail_d_col = None, None, None
                                    for i, row in enumerate(ws_fail.iter_rows(min_row=1, max_row=5)):
                                        for cell in row:
                                            if cell.value == '物料编码':
                                                fail_c_col = cell.column
                                            if cell.value == '数量':
                                                fail_d_col = cell.column
                                        if fail_c_col and fail_d_col:
                                            fail_header_row = i + 1
                                            break
                                    if fail_c_col and fail_d_col and fail_header_row:
                                        for r in range(fail_header_row+1, ws_fail.max_row+1):
                                            fail_code = ws_fail.cell(row=r, column=fail_c_col).value
                                            if str(fail_code).strip() == str(code).strip():
                                                ws_fail.cell(row=r, column=fail_d_col, value=qty)                                
                                                fail_wb.save(fail_path)
                wb.save(bom_path)
        
        return output_files, residual_list 

def merge_sleeve_data_by_cabinet(sleeve_counts_df):
    """
    按屏柜编号和套管星瀚编码分组，合并系统分子
    :param sleeve_counts_df: 套管匹配DataFrame
    :return: 合并后的DataFrame，包含屏柜编号、套管星瀚编码、系统分子
    """
    if sleeve_counts_df is None or sleeve_counts_df.empty:
        return pd.DataFrame(columns=['屏柜编号', '套管星瀚编码', '系统分子'])
    grouped = sleeve_counts_df.groupby(['屏柜编号', '套管星瀚编码'], as_index=False)['系统分子'].sum()
    return grouped 

def get_column_indices_from_auxiliary_sheet(sheet):
    """
    从配套物料表中动态获取列索引
    :param sheet: openpyxl工作表对象
    :return: (remark_col_idx, code_col_idx, ratio_col_idx, xinghan_code_col_idx) 元组，如果未找到则返回对应的None
    """
    remark_col_idx = None      # "备注"列索引
    code_col_idx = None        # "深瑞星空编码"列索引
    ratio_col_idx = None       # "比例"列索引
    xinghan_code_col_idx = None # "星瀚编码"列索引

    # 查找表头行和列索引
    for row_idx in range(1, min(11, sheet.max_row + 1)):  # 在前10行中查找表头
        for col_idx in range(1, sheet.max_column + 1):
            cell_value = sheet.cell(row=row_idx, column=col_idx).value
            if cell_value:
                cell_str = str(cell_value).strip()
                if cell_str == "备注":
                    remark_col_idx = col_idx
                elif cell_str == "深瑞星空编码":
                    code_col_idx = col_idx
                elif cell_str == "比例":
                    ratio_col_idx = col_idx
                elif cell_str == "星瀚编码":
                    xinghan_code_col_idx = col_idx

        # 如果找到了所有关键列，就停止查找
        if all(idx is not None for idx in [remark_col_idx, code_col_idx, ratio_col_idx, xinghan_code_col_idx]):
            break

    return remark_col_idx, code_col_idx, ratio_col_idx, xinghan_code_col_idx


def get_fixed_ratio_auxiliary_materials(material_list_file):
    """
    从物料辅料与把手文件中获取固定比例的配套辅料信息
    :param material_list_file: 物料辅料与把手文件路径
    :return: {主料编码: {'主料比例': x, '配套辅料': [{'星瀚编码': xxx, '比例': y}, ...]}, ...}
    """
    fixed_ratio_materials = {}
    try:
        wb = openpyxl.load_workbook(material_list_file)
        if '配套物料' not in wb.sheetnames:
            wb.close()
            return fixed_ratio_materials

        sheet = wb['配套物料']

        # 获取列索引
        remark_col_idx, code_col_idx, ratio_col_idx, xinghan_code_col_idx = get_column_indices_from_auxiliary_sheet(sheet)

        # 检查是否找到了必要的列
        if not all(idx is not None for idx in [remark_col_idx, code_col_idx, ratio_col_idx, xinghan_code_col_idx]):
            print(f"警告: 在'配套辅料'表中未找到必要的列（备注、深瑞星空编码、比例、星瀚编码）")
            wb.close()
            return fixed_ratio_materials

        # 查找"备注"列的合并单元格
        f_col_merged = []
        for merged_range in sheet.merged_cells.ranges:
            min_col, min_row, max_col, max_row = merged_range.bounds
            if min_col == remark_col_idx and max_col == remark_col_idx:
                f_col_merged.append((min_row, max_row))

        # 处理每个合并单元格组
        for min_row, max_row in f_col_merged:
            # 检查备注列是否为"固定比例"
            remark_value = sheet.cell(row=min_row, column=remark_col_idx).value
            if remark_value and str(remark_value).strip() == "固定比例":
                # 获取这个组内的所有物料信息
                group_materials = []
                main_material_code = None
                main_material_ratio = None

                for row in range(min_row, max_row + 1):
                    code_value = sheet.cell(row=row, column=code_col_idx).value
                    ratio_value = sheet.cell(row=row, column=ratio_col_idx).value
                    xinghan_code_value = sheet.cell(row=row, column=xinghan_code_col_idx).value

                    if code_value and str(code_value).strip():
                        code = str(code_value).strip()
                        try:
                            ratio = float(ratio_value) if ratio_value is not None else 0.0
                            xinghan_code = str(xinghan_code_value).strip() if xinghan_code_value else ""

                            # 第一行作为主料
                            if row == min_row:
                                main_material_code = code
                                main_material_ratio = ratio
                            else:
                                # 其他行作为配套辅料
                                if xinghan_code:
                                    group_materials.append({
                                        '星瀚编码': xinghan_code,
                                        '比例': ratio
                                    })
                        except (ValueError, TypeError):
                            continue

                # 存储主料和其配套辅料信息
                if main_material_code and main_material_ratio is not None:
                    try:
                        main_code_num = int(''.join(filter(str.isdigit, main_material_code)))
                        fixed_ratio_materials[main_code_num] = {
                            '主料比例': main_material_ratio,
                            '配套辅料': group_materials
                        }
                    except (ValueError, TypeError):
                        continue

        wb.close()

    except Exception as e:
        print(f"获取固定比例配套辅料时出错: {e}")
        import traceback
        traceback.print_exc()

    return fixed_ratio_materials


def calculate_fixed_ratio_quantities(main_material_qty, main_material_ratio, auxiliary_materials):
    """
    计算固定比例配套辅料的数量
    :param main_material_qty: 主料数量
    :param main_material_ratio: 主料比例
    :param auxiliary_materials: 配套辅料列表 [{'星瀚编码': xxx, '比例': y}, ...]
    :return: [{'星瀚编码': xxx, '分子': calculated_qty}, ...]
    """
    from decimal import Decimal, ROUND_HALF_UP

    result = []

    if main_material_ratio == 0:
        # print(f"警告: 主料比例为0，无法计算配套辅料数量")
        return result

    try:
        # 使用Decimal进行精确计算
        main_qty_decimal = Decimal(str(main_material_qty))
        main_ratio_decimal = Decimal(str(main_material_ratio))

        for aux_material in auxiliary_materials:
            aux_ratio = aux_material.get('比例', 0)
            xinghan_code = aux_material.get('星瀚编码', '')

            if aux_ratio != 0 and xinghan_code:
                aux_ratio_decimal = Decimal(str(aux_ratio))

                # 计算公式：配套辅料分子数量 = 主料数量 × (配套辅料比例 / 主料比例)
                calculated_qty = main_qty_decimal * (aux_ratio_decimal / main_ratio_decimal)

                # 保留小数点后两位
                calculated_qty = calculated_qty.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

                result.append({
                    '星瀚编码': xinghan_code,
                    '分子': float(calculated_qty)
                })

    except Exception as e:
        print(f"计算固定比例数量时出错: {e}")
        import traceback
        traceback.print_exc()

    return result


def get_auxiliary_materials_from_cabinet_bom(cabinet_bom_file, material_list_file):
    """
    从屏柜BOM表和物料辅料与把手中获取配套辅料（先合并物料编码再查找配套辅料）
    :param cabinet_bom_file: 屏柜BOM表文件路径
    :param material_list_file: 物料辅料与把手文件路径
    :return: {屏柜号: 配套辅料总行数, ...} 的字典
    """
    auxiliary_materials = {}
    try:
        # 读取配套物料清单，建立编码到配套行数的映射
        wb = openpyxl.load_workbook(material_list_file)
        sheet = wb['配套物料']

        # 动态获取列索引
        remark_col_idx, code_col_idx, ratio_col_idx, xinghan_code_col_idx = get_column_indices_from_auxiliary_sheet(sheet)

        # 检查是否找到了必要的列（至少需要备注和深瑞星空编码列）
        if remark_col_idx is None or code_col_idx is None:
            print(f"警告: 在'配套物料'表中未找到必要的列")
            print(f"备注列索引: {remark_col_idx}, 深瑞星空编码列索引: {code_col_idx}")
            wb.close()
            return auxiliary_materials

        # 查找"备注"列的合并单元格
        f_col_merged = []
        for merged_range in sheet.merged_cells.ranges:
            min_col, min_row, max_col, max_row = merged_range.bounds
            if min_col == remark_col_idx and max_col == remark_col_idx:
                f_col_merged.append((min_row, max_row))

        code_to_auxiliary_count = {}
        for min_row, max_row in f_col_merged:
            # 检查备注列是否为"固定比例"，如果是则跳过（固定比例的配套辅料不需要预留空值）
            remark_value = sheet.cell(row=min_row, column=remark_col_idx).value
            if remark_value and str(remark_value).strip() == "固定比例":
                continue

            for row in range(min_row, max_row + 1):
                code_value = sheet.cell(row=row, column=code_col_idx).value
                if code_value and str(code_value).strip():
                    code = str(code_value).strip()
                    try:
                        code_num = int(''.join(filter(str.isdigit, code)))
                        auxiliary_row_count = max_row - min_row + 1
                        code_to_auxiliary_count[code_num] = auxiliary_row_count
                    except:
                        continue
        wb.close()
        # 读取屏柜BOM表，先合并每个屏柜的物料编码及数量
        with pd.ExcelFile(cabinet_bom_file) as bom_xls:
            for sheet_name in bom_xls.sheet_names:
                df = pd.read_excel(cabinet_bom_file, sheet_name=sheet_name, header=0)
                header_row_idx = None
                position_col_idx = None
                code_col_idx = None
                qty_col_idx = None
                for i in range(min(10, df.shape[0])):
                    row_values = [str(cell).strip() for cell in df.iloc[i, :]]
                    if any('物料编码' in val for val in row_values):
                        header_row_idx = i
                        header = row_values
                        for idx, col_name in enumerate(header):
                            if '屏柜号' in col_name:
                                position_col_idx = idx
                            elif '物料编码' in col_name:
                                code_col_idx = idx
                            elif '数量' in col_name:
                                qty_col_idx = idx
                        break
                if header_row_idx is not None and code_col_idx is not None and position_col_idx is not None and qty_col_idx is not None:
                    # 合并每个屏柜的物料编码及数量
                    cabinet_code_qty = {}
                    for row_idx in range(header_row_idx + 1, df.shape[0]):
                        code_value = df.iat[row_idx, code_col_idx]
                        position_value = df.iat[row_idx, position_col_idx]
                        qty_value = df.iat[row_idx, qty_col_idx]
                        if code_value and str(code_value) != 'nan' and str(code_value).strip():
                            if position_value and str(position_value).strip():
                                position_str = str(position_value).strip().replace('~', '-')
                                code_str = str(code_value).strip()
                                try:
                                    code_num = int(''.join(filter(str.isdigit, code_str)))
                                    qty = 1
                                    if qty_value and str(qty_value).strip() and str(qty_value).strip() != 'nan':
                                        try:
                                            qty = int(float(qty_value))
                                        except:
                                            qty = 1
                                    if position_str not in cabinet_code_qty:
                                        cabinet_code_qty[position_str] = {}
                                    cabinet_code_qty[position_str][code_num] = cabinet_code_qty[position_str].get(code_num, 0) + qty
                                except (ValueError, TypeError):
                                    continue
                    # 对每个屏柜，统计所有有配套辅料的物料的配套行数总和
                    for position_str, code_qty_dict in cabinet_code_qty.items():
                        total_aux_rows = 0
                        for code_num in code_qty_dict:
                            if code_num in code_to_auxiliary_count:
                                total_aux_rows += code_to_auxiliary_count[code_num]
                        if total_aux_rows > 0:
                            auxiliary_materials[position_str] = total_aux_rows
    except Exception as e:
        print(f"获取配套辅料时出错: {e}")
        import traceback
        traceback.print_exc()
    return auxiliary_materials

def write_auxiliary_materials_to_bom(auxiliary_materials, material_library_path, bom_sheet, write_row, d_col, h_col, i_col, position):
    """
    为配套辅料在BOM清单中填入空值
    :param auxiliary_materials: 配套辅料字典（存储行数）
    :param material_library_path: 物料库文件路径（此参数保留但不使用）
    :param bom_sheet: BOM工作表
    :param write_row: 写入起始行
    :param d_col: 星瀚编码列
    :param h_col: 数量列
    :param i_col: 单位列
    :param position: 屏柜号
    :return: 更新后的写入行号
    """
    try:
        # print(f"=== 配套辅料空值填入 ===")
        # print(f"屏柜号: {position}")
        
        # 获取当前屏柜的配套辅料行数
        if position in auxiliary_materials:
            auxiliary_row_count = auxiliary_materials[position]
            # print(f"为屏柜 {position} 填入 {auxiliary_row_count} 行配套辅料空值")
            
            # 直接填入对应数量的空值
            for i in range(auxiliary_row_count):
                bom_sheet.cell(row=write_row, column=d_col, value=None)  # 填入空值
                bom_sheet.cell(row=write_row, column=h_col, value=None)  # 数量列也填入空值
                bom_sheet.cell(row=write_row, column=i_col, value=None)  # 单位列也填入空值
                write_row += 1
            
            # print(f"✅ 总共为屏柜 {position} 填入了 {auxiliary_row_count} 行配套辅料空值")
        else:
            print(f"屏柜 {position} 无配套辅料，无需填入空值")
            pass
            
    except Exception as e:
        print(f"填入配套辅料空值时出错: {e}")
        import traceback
        traceback.print_exc()
    
    return write_row 

def get_g_column_data_from_bom(bom_file, sheet_name=None, start_row=3):
    """
    获取屏柜BOM表G列（数量）数据及其类型
    :param bom_file: 屏柜BOM表路径
    :param sheet_name: 指定sheet名，默认第一个
    :param start_row: 从第几行开始（0为第一行，3为第4行）
    :return: [{'row': 行号, 'value': 值, 'type': 类型}, ...]
    """
    xls = pd.ExcelFile(bom_file)
    if sheet_name is None:
        sheet_name = xls.sheet_names[0]
    df = pd.read_excel(bom_file, sheet_name=sheet_name, header=0)
    results = []
    for idx in range(start_row, df.shape[0]):
        # G列为第7列，索引为6
        if df.shape[1] > 6:
            value = df.iat[idx, 6]
            value_type = type(value).__name__
            results.append({'row': idx + 1, 'value': value, 'type': value_type})
    return results 

def truncate_to_decimals(value, decimals):
    """
    使用decimal模块进行精确的小数截断
    :param value: 要截断的数值
    :param decimals: 保留的小数位数
    :return: 截断后的值
    """
    # 导入decimal计算模块
    from decimal_calculator import precise_truncate_to_decimals
    return precise_truncate_to_decimals(value, decimals)