# preprocess_wiring_table.py

import pandas as pd
import logging
import os
import sys
import re


# 配置日志
def setup_logging():
    """配置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(sys.argv[0])), 'logs')
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, 'preprocess_wiring_table.log')

    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, mode='a'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('PreprocessWiringTable')


# 初始化日志
logger = setup_logging()
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志


def preprocess_wiring_table(input_file_path):
    """
    预处理屏柜配线套管表
    功能：
    1. 处理屏柜编号中的"~"符号，替换为"-"（例如：P01~P02 -> P01-P02）
    2. 处理导线起点和终点中的回车符
    3. 根据设备标号智能识别设备类型（如CLP→压板，LAMP→照明灯，RG→其他等）
    4. 补充设备类型信息
    5. 补充颜色/线径标识信息
    """
    try:
        logger.info(f"开始预处理屏柜配线套管表: {input_file_path}")

        # 读取Excel文件，跳过前两行（表头和标题行）
        df = pd.read_excel(input_file_path, header=2)
        logger.info(f"成功读取文件, 总行数: {len(df)}")

        # 处理屏柜编号中的"~"符号，替换为"-"
        cabinet_number_col = None
        for col in df.columns:
            if "屏柜编号" in col:
                cabinet_number_col = col
                break

        if cabinet_number_col:
            logger.info(f"找到屏柜编号列: {cabinet_number_col}")
            replaced_count = 0
            replacement_details = []

            for idx, value in df[cabinet_number_col].items():
                if pd.notna(value) and isinstance(value, str) and '~' in str(value):
                    original_value = str(value)
                    new_value = original_value.replace('~', '-')
                    df.at[idx, cabinet_number_col] = new_value
                    replaced_count += 1
                    replacement_details.append(f"{original_value} -> {new_value}")
                    logger.info(f"屏柜编号替换: {original_value} -> {new_value}")

            if replaced_count > 0:
                logger.info(f"屏柜编号处理完成: 共处理 {replaced_count} 个包含'~'的屏柜编号")
                for detail in replacement_details:
                    logger.info(f"  {detail}")
            else:
                logger.info("屏柜编号列: 未发现包含'~'的数据")
        else:
            logger.warning("未找到屏柜编号列，跳过屏柜编号处理")

        # 获取列名
        device_type_col = None
        wire_start_col = None
        wire_end_col = None

        for col in df.columns:
            if "设备类型" in col and "起点/终点" in col:
                device_type_col = col
            elif "导线起点" in col:
                wire_start_col = col
            elif "导线终点" in col:
                wire_end_col = col

        if not device_type_col or not wire_start_col:
            logger.error("未找到必要的列：'设备类型（起点/终点）'或'导线起点'")
            raise ValueError("未找到必要的列：'设备类型（起点/终点）'或'导线起点'")

        logger.info(f"找到设备类型列: {device_type_col}, 导线起点列: {wire_start_col}")
        if wire_end_col:
            logger.info(f"找到导线终点列: {wire_end_col}")

        # 新增：处理回车符功能
        def replace_newlines_with_slash(text):
            """将文本中的回车符替换为斜杠"""
            if pd.isna(text) or not isinstance(text, str):
                return text
            
            # 替换各种类型的换行符
            text = str(text)
            text = text.replace('\r\n', '/')  # Windows换行符
            text = text.replace('\n', '/')    # Unix换行符
            text = text.replace('\r', '/')    # Mac换行符
            
            return text

        # 处理导线起点列的回车符
        if wire_start_col:
            original_count = 0
            replaced_count = 0
            
            for idx, value in df[wire_start_col].items():
                if pd.notna(value) and isinstance(value, str) and ('\n' in str(value) or '\r' in str(value)):
                    original_count += 1
                    df.at[idx, wire_start_col] = replace_newlines_with_slash(value)
                    replaced_count += 1
            
            if replaced_count > 0:
                logger.info(f"导线起点列: 共处理 {replaced_count} 个包含回车符的单元格")
            else:
                logger.info("导线起点列: 未发现包含回车符的数据")

        # 处理导线终点列的回车符
        if wire_end_col:
            original_count = 0
            replaced_count = 0
            
            for idx, value in df[wire_end_col].items():
                if pd.notna(value) and isinstance(value, str) and ('\n' in str(value) or '\r' in str(value)):
                    original_count += 1
                    df.at[idx, wire_end_col] = replace_newlines_with_slash(value)
                    replaced_count += 1
            
            if replaced_count > 0:
                logger.info(f"导线终点列: 共处理 {replaced_count} 个包含回车符的单元格")
            else:
                logger.info("导线终点列: 未发现包含回车符的数据")

        # 预处理函数
        def fill_device_type(row):
            device_type = row[device_type_col]
            wire_start = row[wire_start_col]

            # 如果设备类型列已经有内容且包含"/"，不做处理
            if not pd.isna(device_type) and isinstance(device_type, str) and "/" in device_type:
                return device_type

            # 分割导线起点
            if not pd.isna(wire_start) and isinstance(wire_start, str) and "/" in wire_start:
                parts = wire_start.split("/")
                if len(parts) >= 2:
                    start_str = parts[0].strip()
                    end_str = parts[1].strip()

                    # 确定起点类型
                    if "屏蔽层" in start_str:
                        start_type = "屏蔽层"
                    elif "GND" in start_str:
                        start_type = "接地铜排"
                    elif "SR" in start_str:
                        start_type = "防雷器"
                    elif "CLP" in start_str:
                        start_type = "压板"
                    elif "KLP" in start_str:
                        start_type = "压板"
                    elif "SLP" in start_str:
                        start_type = "压板"
                    elif "LAMP" in start_str:
                        start_type = "照明灯"
                    elif "KG" in start_str:
                        start_type = "行程开关"
                    elif "RG" in start_str:
                        start_type = "其他"
                    elif "PP" in start_str:
                        start_type = "其他"
                    elif "n" in start_str:
                        start_type = "装置"
                    elif any(char in start_str for char in ["D", "d"]):
                        start_type = "端子排"
                    else:
                        start_type = "其他"  # 默认值

                    # 确定终点类型
                    if "屏蔽层" in end_str:
                        end_type = "屏蔽层"
                    elif "GND" in end_str:
                        end_type = "接地铜排"
                    elif "SR" in end_str:
                        end_type = "防雷器"
                    elif "CLP" in end_str:
                        end_type = "压板"
                    elif "KLP" in end_str:
                        end_type = "压板"
                    elif "SLP" in end_str:
                        end_type = "压板"
                    elif "LAMP" in end_str:
                        end_type = "照明灯"
                    elif "KG" in end_str:
                        end_type = "行程开关"
                    elif "RG" in end_str:
                        end_type = "其他"
                    elif "PP" in end_str:
                        end_type = "其他"
                    elif "n" in end_str:
                        end_type = "装置"
                    elif any(char in end_str for char in ["D", "d"]):
                        end_type = "端子排"
                    else:
                        end_type = "其他"  # 默认值

                    # 组合新的设备类型
                    return f"{start_type}/{end_type}"

            # 如果无法处理，保持原值
            return device_type

        # 应用预处理
        df[device_type_col] = df.apply(fill_device_type, axis=1)
        logger.info(f"预处理完成，已更新设备类型列")

        # 获取颜色/线径标识列
        color_diameter_col = None
        for col in df.columns:
            if "颜色/线径标识" in col:
                color_diameter_col = col
                break
        
        if color_diameter_col:
            logger.info(f"找到颜色/线径标识列: {color_diameter_col}")
            
            # 新增：颜色/线径标识清洗函数
            def clean_color_diameter(value):
                if pd.isna(value):
                    return value
                s = str(value).strip()
                # 匹配如 ;蓝;($) 或 ; 棕 ;($) 之类，去除多余分号和空格
                # 只保留第一个中文或英文颜色名+($)的组合
                # 先去除所有分号和多余空格
                s = re.sub(r'[;；\s]+', '', s)
                # 匹配如 蓝($) 棕($) 黑($) 黄(U) 绿(I) 等
                m = re.match(r'([\u4e00-\u9fa5A-Za-z]+\([^\)]*\))', s)
                if m:
                    return m.group(1)
                # 如果没有括号，保留第一个中文或英文
                m2 = re.match(r'([\u4e00-\u9fa5A-Za-z]+)', s)
                if m2:
                    return m2.group(1)
                return s
            
            # 颜色/线径标识补充函数
            def fill_color_diameter(row):
                color_diameter = row[color_diameter_col]
                device_type = row[device_type_col]
                
                # 如果颜色/线径标识不为空，清洗后返回
                if not pd.isna(color_diameter) and str(color_diameter).strip():
                    return clean_color_diameter(color_diameter)
                
                # 根据设备类型确定填充值
                device_type_str = str(device_type) if not pd.isna(device_type) else ""
                
                has_ground = "接地铜排" in device_type_str
                has_shield = "屏蔽层" in device_type_str
                
                if has_ground and not has_shield:
                    return "花(**)"
                elif not has_ground and has_shield:
                    return "屏蔽层"
                elif not has_ground and not has_shield:
                    return "未标识"
                else:
                    # 既有接地铜排又有屏蔽层的情况，保持原值或填充未标识
                    return clean_color_diameter(color_diameter) if not pd.isna(color_diameter) else "未标识"
            
            # 应用颜色/线径标识预处理
            df[color_diameter_col] = df.apply(fill_color_diameter, axis=1)
            logger.info(f"预处理完成，已更新颜色/线径标识列")
        else:
            logger.warning("未找到颜色/线径标识列，跳过该项处理")

        return df

    except Exception as e:
        logger.exception("预处理屏柜配线套管表时发生错误")
        raise
